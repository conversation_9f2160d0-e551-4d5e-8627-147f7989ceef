package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 用户创建DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserCreateDTO", description = "用户创建请求参数")
public class UserCreateDTO extends BaseCreateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户名(邮箱)", required = true, example = "<EMAIL>")
    @NotBlank(message = "用户名不能为空")
    @Email(message = "用户名必须是有效的邮箱格式")
    @Size(max = 128, message = "用户名长度不能超过128个字符")
    private String username;

    @ApiModelProperty(value = "密码", required = true, example = "password123")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 256, message = "密码长度必须在6-256个字符之间")
    private String password;

    @ApiModelProperty(value = "用户的唯一码", example = "USER001")
    @Size(max = 32, message = "用户唯一码长度不能超过32个字符")
    private String code;

    @ApiModelProperty(value = "账户余额", example = "0.0000")
    private BigDecimal balance;

    @ApiModelProperty(value = "注册时间", example = "1692345600000")
    private Long registerTime;
}
