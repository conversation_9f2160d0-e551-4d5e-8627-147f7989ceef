package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseUpdateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;

/**
 * 用户余额流水更新DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserBalanceLogUpdateDTO", description = "用户余额流水更新请求参数")
public class UserBalanceLogUpdateDTO extends BaseUpdateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联交易ID", example = "123", notes = "充值payment_trade表，消费billing_record表，退款refund_order表")
    private Long tradeId;

    @ApiModelProperty(value = "备注", example = "账户充值")
    @Size(max = 64, message = "备注长度不能超过64个字符")
    private String remark;

    // 注意：用户ID、余额类型、金额等核心字段不允许修改
}
