package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 用户API密钥创建DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserApiKeyCreateDTO", description = "用户API密钥创建请求参数")
public class UserApiKeyCreateDTO extends BaseCreateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "备注", example = "开发环境使用")
    @Size(max = 64, message = "备注长度不能超过64个字符")
    private String remark;

    // API密钥由系统自动生成，不需要用户输入
    // 状态默认为正常，最近访问时间初始化为0
}
