package com.cloudpod.podsail.common.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 基础更新DTO
 * 用于更新实体的请求参数
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public abstract class BaseUpdateDTO extends BaseRequestDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "ID不能为空")
    private Long id;

    // 更新时间、更新人等字段由系统自动填充
}
