package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.user.UserBalanceLogCreateDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogQueryDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogResponseDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogUpdateDTO;
import com.cloudpod.podsail.service.UserBalanceLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户余额流水管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user-balance-logs")
@Api(tags = "用户余额流水管理", description = "用户余额流水相关的CRUD操作接口")
public class UserBalanceLogController {

    @Autowired
    private UserBalanceLogService userBalanceLogService;

    @PostMapping
    @ApiOperation(value = "创建用户余额流水", notes = "创建新的用户余额流水记录")
    public Response<UserBalanceLogResponseDTO> createUserBalanceLog(
            @ApiParam(value = "用户余额流水创建信息", required = true)
            @RequestBody @Valid UserBalanceLogCreateDTO createDTO) {
        
        log.info("创建用户余额流水，请求参数: {}", createDTO);
        UserBalanceLogResponseDTO responseDTO = userBalanceLogService.createUserBalanceLog(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新用户余额流水", notes = "更新用户余额流水信息")
    public Response<UserBalanceLogResponseDTO> updateUserBalanceLog(
            @ApiParam(value = "用户余额流水更新信息", required = true)
            @RequestBody @Valid UserBalanceLogUpdateDTO updateDTO) {
        
        log.info("更新用户余额流水，请求参数: {}", updateDTO);
        UserBalanceLogResponseDTO responseDTO = userBalanceLogService.updateUserBalanceLog(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户余额流水", notes = "根据用户余额流水ID查询详细信息")
    public Response<UserBalanceLogResponseDTO> getUserBalanceLogById(
            @ApiParam(value = "用户余额流水ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询用户余额流水，ID: {}", id);
        UserBalanceLogResponseDTO responseDTO = userBalanceLogService.getUserBalanceLogById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除用户余额流水", notes = "根据用户余额流水ID删除（逻辑删除）")
    public Response<Boolean> deleteUserBalanceLog(
            @ApiParam(value = "用户余额流水ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除用户余额流水，ID: {}", id);
        boolean result = userBalanceLogService.deleteEntityById(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除用户余额流水", notes = "根据用户余额流水ID列表批量删除（逻辑删除）")
    public Response<Boolean> deleteUserBalanceLogs(
            @ApiParam(value = "用户余额流水ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除用户余额流水，ID列表: {}", ids);
        boolean result = userBalanceLogService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户余额流水", notes = "根据条件分页查询用户余额流水列表")
    public Response<IPage<UserBalanceLogResponseDTO>> getUserBalanceLogPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserBalanceLogQueryDTO queryDTO) {
        
        log.info("分页查询用户余额流水，查询条件: {}", queryDTO);
        IPage<UserBalanceLogResponseDTO> page = userBalanceLogService.getUserBalanceLogPage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询用户余额流水列表", notes = "根据条件查询用户余额流水列表")
    public Response<List<UserBalanceLogResponseDTO>> getUserBalanceLogList(
            @ApiParam(value = "查询条件")
            @RequestBody UserBalanceLogQueryDTO queryDTO) {
        
        log.info("查询用户余额流水列表，查询条件: {}", queryDTO);
        List<UserBalanceLogResponseDTO> list = userBalanceLogService.getUserBalanceLogList(queryDTO);
        return Response.success(list);
    }

    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID查询余额流水", notes = "查询指定用户的所有余额流水记录")
    public Response<List<UserBalanceLogResponseDTO>> getUserBalanceLogsByUserId(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("根据用户ID查询余额流水，用户ID: {}", userId);
        var userBalanceLogs = userBalanceLogService.getUserBalanceLogsByUserId(userId);
        List<UserBalanceLogResponseDTO> responseDTOList = userBalanceLogs.stream()
                .map(userBalanceLog -> userBalanceLogService.getUserBalanceLogById(userBalanceLog.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/user/{userId}/type/{type}")
    @ApiOperation(value = "根据用户ID和类型查询余额流水", notes = "查询指定用户指定类型的余额流水记录")
    public Response<List<UserBalanceLogResponseDTO>> getUserBalanceLogsByUserIdAndType(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId,
            @ApiParam(value = "余额类型", required = true, notes = "1-充值 2-消费 3-退款")
            @PathVariable("type") @NotNull Integer type) {
        
        log.info("根据用户ID和类型查询余额流水，用户ID: {}, 类型: {}", userId, type);
        var userBalanceLogs = userBalanceLogService.getUserBalanceLogsByUserIdAndType(userId, type);
        List<UserBalanceLogResponseDTO> responseDTOList = userBalanceLogs.stream()
                .map(userBalanceLog -> userBalanceLogService.getUserBalanceLogById(userBalanceLog.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/trade/{tradeId}")
    @ApiOperation(value = "根据交易ID查询余额流水", notes = "根据交易ID查询对应的余额流水记录")
    public Response<UserBalanceLogResponseDTO> getUserBalanceLogByTradeId(
            @ApiParam(value = "交易ID", required = true)
            @PathVariable("tradeId") @NotNull Long tradeId) {
        
        log.info("根据交易ID查询余额流水，交易ID: {}", tradeId);
        var userBalanceLog = userBalanceLogService.getUserBalanceLogByTradeId(tradeId);
        if (userBalanceLog == null) {
            return Response.success(null);
        }
        UserBalanceLogResponseDTO responseDTO = userBalanceLogService.getUserBalanceLogById(userBalanceLog.getId());
        return Response.success(responseDTO);
    }

    @GetMapping("/user/{userId}/total/recharge")
    @ApiOperation(value = "查询用户总充值金额", notes = "计算指定用户的总充值金额")
    public Response<BigDecimal> getTotalRechargeAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户总充值金额，用户ID: {}", userId);
        BigDecimal totalAmount = userBalanceLogService.getTotalRechargeAmount(userId);
        return Response.success(totalAmount);
    }

    @GetMapping("/user/{userId}/total/consume")
    @ApiOperation(value = "查询用户总消费金额", notes = "计算指定用户的总消费金额")
    public Response<BigDecimal> getTotalConsumeAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户总消费金额，用户ID: {}", userId);
        BigDecimal totalAmount = userBalanceLogService.getTotalConsumeAmount(userId);
        return Response.success(totalAmount);
    }

    @GetMapping("/user/{userId}/total/refund")
    @ApiOperation(value = "查询用户总退款金额", notes = "计算指定用户的总退款金额")
    public Response<BigDecimal> getTotalRefundAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户总退款金额，用户ID: {}", userId);
        BigDecimal totalAmount = userBalanceLogService.getTotalRefundAmount(userId);
        return Response.success(totalAmount);
    }

    @PostMapping("/recharge")
    @ApiOperation(value = "创建充值流水", notes = "创建用户充值流水记录")
    public Response<UserBalanceLogResponseDTO> createRechargeLog(
            @ApiParam(value = "用户ID", required = true) @RequestParam("userId") @NotNull Long userId,
            @ApiParam(value = "充值金额", required = true) @RequestParam("amount") @NotNull BigDecimal amount,
            @ApiParam(value = "交易ID") @RequestParam(value = "tradeId", required = false) Long tradeId,
            @ApiParam(value = "备注") @RequestParam(value = "remark", required = false) String remark) {
        
        log.info("创建充值流水，用户ID: {}, 金额: {}, 交易ID: {}, 备注: {}", userId, amount, tradeId, remark);
        UserBalanceLogResponseDTO responseDTO = userBalanceLogService.createRechargeLog(userId, amount, tradeId, remark);
        return Response.success(responseDTO);
    }

    @PostMapping("/consume")
    @ApiOperation(value = "创建消费流水", notes = "创建用户消费流水记录")
    public Response<UserBalanceLogResponseDTO> createConsumeLog(
            @ApiParam(value = "用户ID", required = true) @RequestParam("userId") @NotNull Long userId,
            @ApiParam(value = "消费金额", required = true) @RequestParam("amount") @NotNull BigDecimal amount,
            @ApiParam(value = "交易ID") @RequestParam(value = "tradeId", required = false) Long tradeId,
            @ApiParam(value = "备注") @RequestParam(value = "remark", required = false) String remark) {
        
        log.info("创建消费流水，用户ID: {}, 金额: {}, 交易ID: {}, 备注: {}", userId, amount, tradeId, remark);
        UserBalanceLogResponseDTO responseDTO = userBalanceLogService.createConsumeLog(userId, amount, tradeId, remark);
        return Response.success(responseDTO);
    }

    @PostMapping("/refund")
    @ApiOperation(value = "创建退款流水", notes = "创建用户退款流水记录")
    public Response<UserBalanceLogResponseDTO> createRefundLog(
            @ApiParam(value = "用户ID", required = true) @RequestParam("userId") @NotNull Long userId,
            @ApiParam(value = "退款金额", required = true) @RequestParam("amount") @NotNull BigDecimal amount,
            @ApiParam(value = "交易ID") @RequestParam(value = "tradeId", required = false) Long tradeId,
            @ApiParam(value = "备注") @RequestParam(value = "remark", required = false) String remark) {
        
        log.info("创建退款流水，用户ID: {}, 金额: {}, 交易ID: {}, 备注: {}", userId, amount, tradeId, remark);
        UserBalanceLogResponseDTO responseDTO = userBalanceLogService.createRefundLog(userId, amount, tradeId, remark);
        return Response.success(responseDTO);
    }
}
